<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!-- Insert global system.web node in any case -->
  <system.web xdt:Transform="InsertIfMissing">
  </system.web>

  <!-- Search for system.web nodes:
        - globally
        - under location[@path='.']
        - under location[count(@path)=0]
        If any of above contains httpModules section - it will be reused. 
        Otherwise it will be created under /configuration/system.web (globally)
  -->
  <system.web xdt:Locator="XPath(//system.web[(count(parent::location) = 0) or (count(parent::location[@path != '.' and count(@path) != 0]) = 0)])">
    <httpModules xdt:Transform="InsertIfMissing">
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" xdt:Transform="Remove" xdt:Locator="Match(type)"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" xdt:Transform="InsertIfMissing" xdt:Locator="Match(type)"/>
    </httpModules>
  </system.web>
  

  <!-- Insert global system.web node in any case -->
  <system.webServer xdt:Transform="InsertIfMissing">
  </system.webServer>
  
  <!-- Only one validation node is allowed to be deinfed globally or in global location tags. See explanaition above for httpModules on how it works -->
  <system.webServer xdt:Locator="XPath(//system.webServer[(count(parent::location) = 0) or (count(parent::location[@path != '.' and count(@path) != 0]) = 0)])">
    <validation validateIntegratedModeConfiguration="false" xdt:Transform="InsertIfMissing" />
  </system.webServer>

  <!-- Only one modules node is allowed to be deinfed globally or in global location tags. 
       Modules node may not be defined in the same place where validation node above is defined.
       We need to remove all customer-renamed modules as remove tag relies on module name attribute
       See explanaition above for httpModules on how it works
  -->
  <system.webServer xdt:Locator="XPath(//system.webServer[(count(parent::location) = 0) or (count(parent::location[@path != '.' and count(@path) != 0]) = 0)])">
    <modules xdt:Transform="InsertIfMissing">
      <remove name="ApplicationInsightsWebTracking" xdt:Transform="InsertIfMissing" xdt:Locator="Match(name)"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler" xdt:Transform="Remove" xdt:Locator="Match(type)"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler" xdt:Transform="InsertIfMissing" xdt:Locator="Match(type)"/>
    </modules>
  </system.webServer>
</configuration>